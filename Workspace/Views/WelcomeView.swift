import SwiftUI

struct WelcomeView: View {
    @StateObject private var hammerspoonManager = HammerspoonManager.shared
    @State private var currentStep = 0
    
    let steps = [
        WelcomeStep(
            title: "歡迎使用 Workspace",
            description: "一個優雅的 macOS 視窗佈局管理工具",
            icon: "rectangle.3.group"
        ),
        WelcomeStep(
            title: "安裝 Hammerspoon",
            description: "Workspace 需要 Hammerspoon 來管理視窗佈局",
            icon: "arrow.down.circle"
        ),
        WelcomeStep(
            title: "配置完成",
            description: "一切就緒！開始管理您的工作區佈局",
            icon: "checkmark.circle"
        )
    ]
    
    var body: some View {
        VStack(spacing: 30) {
            // 進度指示器
            HStack(spacing: 8) {
                ForEach(0..<steps.count, id: \.self) { index in
                    Circle()
                        .fill(index <= currentStep ? Color.accentColor : Color.secondary.opacity(0.3))
                        .frame(width: 8, height: 8)
                }
            }
            
            // 當前步驟內容
            VStack(spacing: 20) {
                Image(systemName: steps[currentStep].icon)
                    .font(.system(size: 48))
                    .foregroundColor(.accentColor)
                
                Text(steps[currentStep].title)
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text(steps[currentStep].description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            
            // 操作按鈕
            VStack(spacing: 12) {
                if currentStep == 1 && !hammerspoonManager.isInstalled {
                    Button("下載 Hammerspoon") {
                        hammerspoonManager.openHammerspoonWebsite()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    
                    Button("我已安裝 Hammerspoon") {
                        hammerspoonManager.checkInstallation()
                        if hammerspoonManager.isInstalled {
                            nextStep()
                        }
                    }
                    .buttonStyle(.bordered)
                } else if currentStep == 1 && hammerspoonManager.isInstalled && !hammerspoonManager.isConfigured {
                    Button("安裝配置檔案") {
                        hammerspoonManager.installConfiguration()
                        nextStep()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                } else if currentStep == 2 {
                    Button("開始使用") {
                        completeSetup()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                } else {
                    Button("繼續") {
                        nextStep()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                }
            }
        }
        .padding(40)
        .frame(width: 400, height: 350)
        .onAppear {
            checkCurrentState()
        }
    }
    
    private func nextStep() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentStep = min(currentStep + 1, steps.count - 1)
        }
    }
    
    private func checkCurrentState() {
        if hammerspoonManager.isInstalled && hammerspoonManager.isConfigured {
            currentStep = 2
        } else if hammerspoonManager.isInstalled {
            currentStep = 1
        }
    }
    
    private func completeSetup() {
        UserDefaults.standard.set(true, forKey: "hasCompletedSetup")
        NSApp.keyWindow?.close()
    }
}

struct WelcomeStep {
    let title: String
    let description: String
    let icon: String
}