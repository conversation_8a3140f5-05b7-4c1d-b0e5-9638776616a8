import SwiftUI

struct SettingsView: View {
    @AppStorage("alertTextSize") private var alertTextSize: Double = 64
    @AppStorage("alertDuration") private var alertDuration: Double = 0.5
    @AppStorage("launchAtLogin") private var launchAtLogin: Bool = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("設定")
                .font(.title2)
                .fontWeight(.semibold)
            
            Form {
                Section("進度提示樣式") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("字體大小: \(Int(alertTextSize))")
                            .font(.subheadline)
                        
                        Slider(value: $alertTextSize, in: 20...100, step: 4) {
                            Text("字體大小")
                        }
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("顯示時間: \(alertDuration, specifier: "%.1f")秒")
                            .font(.subheadline)
                        
                        Slider(value: $alertDuration, in: 0.2...2.0, step: 0.1) {
                            Text("顯示時間")
                        }
                    }
                }
                
                Section("應用程式") {
                    Toggle("開機時自動啟動", isOn: $launchAtLogin)
                        .onChange(of: launchAtLogin) { newValue in
                            setLaunchAtLogin(newValue)
                        }
                }
            }
            .formStyle(.grouped)
            
            HStack {
                Button("重設為預設值") {
                    alertTextSize = 64
                    alertDuration = 0.5
                    launchAtLogin = false
                }
                .buttonStyle(.bordered)
                
                Spacer()
                
                Button("完成") {
                    NSApp.keyWindow?.close()
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding(24)
        .frame(width: 400, height: 300)
    }
    
    private func setLaunchAtLogin(_ enabled: Bool) {
        let bundleIdentifier = Bundle.main.bundleIdentifier!
        let loginItems = LSSharedFileList.create(nil, kLSSharedFileListSessionLoginItems.takeRetainedValue(), nil)
        
        if enabled {
            let appURL = Bundle.main.bundleURL
            LSSharedFileListInsertItemURL(loginItems?.takeRetainedValue(), kLSSharedFileListItemLast.takeRetainedValue(), nil, nil, appURL as CFURL, nil, nil)
        } else {
            // 移除登入項目的邏輯
            // 這裡需要更複雜的實作來找到並移除項目
        }
    }
}