struct ProfileRowView: View {
    let profile: Profile
    let onRestore: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        Button(action: onRestore) {
            HStack(spacing: 12) {
                // Profile 預覽縮圖 - 顯示應用程式圖示
                ZStack {
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.accentColor.opacity(0.1))
                        .frame(width: 40, height: 30)
                    
                    HStack(spacing: -4) {
                        ForEach(Array(profile.windows.prefix(3).enumerated()), id: \.offset) { index, window in
                            if let icon = AppIconProvider.getIcon(for: window.bundleID) {
                                Image(nsImage: icon)
                                    .resizable()
                                    .frame(width: 12, height: 12)
                                    .clipShape(RoundedRectangle(cornerRadius: 2))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 2)
                                            .stroke(Color.white, lineWidth: 0.5)
                                    )
                                    .zIndex(Double(3 - index))
                            }
                        }
                        
                        if profile.windows.count > 3 {
                            Text("+\(profile.windows.count - 3)")
                                .font(.system(size: 8, weight: .medium))
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(profile.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .lineLimit(1)
                    
                    HStack(spacing: 4) {
                        Text(profile.previewDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.tertiary)
                        
                        Text(RelativeDateTimeFormatter().localizedString(for: profile.modifiedAt, relativeTo: Date()))
                            .font(.caption)
                            .foregroundColor(.tertiary)
                    }
                }
                
                Spacer()
                
                Button(action: onEdit) {
                    Image(systemName: "pencil")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
                .help("編輯")
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .hoverEffect()
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.clear)
                .contentShape(Rectangle())
        )
    }
}