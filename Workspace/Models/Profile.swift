import Foundation

struct Profile: Identifiable, Codable {
    let id = UUID()
    var name: String
    var windows: [WindowLayout]
    var createdAt: Date
    var modifiedAt: Date
    
    init(name: String, windows: [WindowLayout] = []) {
        self.name = name
        self.windows = windows
        self.createdAt = Date()
        self.modifiedAt = Date()
    }
}

struct WindowLayout: Identifiable, Codable {
    let id = UUID()
    let app: String
    let bundleID: String
    let title: String
    let frame: WindowFrame
    
    struct WindowFrame: Codable {
        let x: Double
        let y: Double
        let w: Double
        let h: Double
    }
}

extension Profile {
    var jsonFileName: String {
        return "\(name).json"
    }
    
    var previewDescription: String {
        return "\(windows.count) 個視窗"
    }
}