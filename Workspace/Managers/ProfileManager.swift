import Foundation
import Combine

class ProfileManager: ObservableObject {
    static let shared = ProfileManager()
    
    @Published var profiles: [Profile] = []
    private let layoutsPath: URL
    
    private init() {
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        layoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")
        
        createLayoutsDirectoryIfNeeded()
        loadProfiles()
    }
    
    private func createLayoutsDirectoryIfNeeded() {
        if !FileManager.default.fileExists(atPath: layoutsPath.path) {
            try? FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)
        }
    }
    
    func loadProfiles() {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: layoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" }
            
            profiles = jsonFiles.compactMap { url in
                loadProfile(from: url)
            }.sorted { $0.modifiedAt > $1.modifiedAt }
        } catch {
            print("載入 Profiles 失敗: \(error)")
        }
    }
    
    private func loadProfile(from url: URL) -> Profile? {
        do {
            let data = try Data(contentsOf: url)
            let windows = try JSONDecoder().decode([WindowLayout].self, from: data)
            let name = url.deletingPathExtension().lastPathComponent
            
            var profile = Profile(name: name, windows: windows)
            
            // 獲取檔案修改時間
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            if let modificationDate = attributes[.modificationDate] as? Date {
                profile.modifiedAt = modificationDate
            }
            
            return profile
        } catch {
            print("載入 Profile \(url.lastPathComponent) 失敗: \(error)")
            return nil
        }
    }
    
    func saveProfile(_ profile: Profile) {
        let url = layoutsPath.appendingPathComponent(profile.jsonFileName)
        
        do {
            let data = try JSONEncoder().encode(profile.windows)
            try data.write(to: url)
            loadProfiles() // 重新載入
        } catch {
            print("儲存 Profile 失敗: \(error)")
        }
    }
    
    func deleteProfile(_ profile: Profile) {
        let url = layoutsPath.appendingPathComponent(profile.jsonFileName)
        
        do {
            try FileManager.default.removeItem(at: url)
            loadProfiles() // 重新載入
        } catch {
            print("刪除 Profile 失敗: \(error)")
        }
    }
    
    func renameProfile(_ profile: Profile, to newName: String) {
        let oldURL = layoutsPath.appendingPathComponent(profile.jsonFileName)
        let newURL = layoutsPath.appendingPathComponent("\(newName).json")
        
        do {
            try FileManager.default.moveItem(at: oldURL, to: newURL)
            loadProfiles() // 重新載入
        } catch {
            print("重命名 Profile 失敗: \(error)")
        }
    }
}