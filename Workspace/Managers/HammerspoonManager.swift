func saveCurrentLayout(profileName: String) {
    let luaCode = "saveWindowLayout('\(profileName)')"
    executeLuaCode(luaCode)
    
    // 延遲顯示成功通知
    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
        NotificationManager.shared.showSuccess(
            title: "佈局已儲存",
            message: "Profile「\(profileName)」已成功儲存"
        )
    }
}

func restoreLayout(profileName: String) {
    let luaCode = "restoreWindowLayout('\(profileName)')"
    executeLuaCode(luaCode)
    
    // 延遲顯示成功通知
    DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
        NotificationManager.shared.showSuccess(
            title: "佈局已還原",
            message: "Profile「\(profileName)」已成功還原"
        )
        NotificationManager.shared.playCompletionSound()
    }
}