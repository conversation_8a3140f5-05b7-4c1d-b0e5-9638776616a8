import Foundation
import AppKit
import Combine

class HammerspoonManager: ObservableObject {
    static let shared = HammerspoonManager()

    @Published var isInstalled = false
    @Published var isConfigured = false
    @Published var isRunning = false

    private let hammerspoonPath = "/Applications/Hammerspoon.app"
    private let configPath: URL
    private let layoutsPath: URL

    private init() {
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        configPath = homeURL.appendingPathComponent(".hammerspoon/init.lua")
        layoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")

        checkInstallation()
        checkConfiguration()
    }

    // MARK: - Installation & Configuration

    func checkInstallation() {
        isInstalled = FileManager.default.fileExists(atPath: hammerspoonPath)

        if isInstalled {
            checkIfRunning()
        }
    }

    private func checkIfRunning() {
        let runningApps = NSWorkspace.shared.runningApplications
        isRunning = runningApps.contains { $0.bundleIdentifier == "org.hammerspoon.Hammerspoon" }
    }

    func checkConfiguration() {
        isConfigured = FileManager.default.fileExists(atPath: configPath.path)
    }

    func openHammerspoonWebsite() {
        if let url = URL(string: "https://www.hammerspoon.org/") {
            NSWorkspace.shared.open(url)
        }
    }

    func installConfiguration() {
        guard let configURL = Bundle.main.url(forResource: "init", withExtension: "lua") else {
            print("找不到配置檔案模板")
            return
        }

        do {
            // 創建 .hammerspoon 目錄
            let hammerspoonDir = configPath.deletingLastPathComponent()
            try FileManager.default.createDirectory(at: hammerspoonDir, withIntermediateDirectories: true)

            // 創建 layouts 目錄
            try FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)

            // 複製配置檔案
            let configContent = try String(contentsOf: configURL)
            try configContent.write(to: configPath, atomically: true, encoding: .utf8)

            isConfigured = true

            // 重新載入 Hammerspoon 配置
            reloadConfiguration()

        } catch {
            print("安裝配置檔案失敗: \(error)")
        }
    }

    // MARK: - Hammerspoon Communication

    private func executeLuaCode(_ code: String) {
        guard isInstalled && isRunning else {
            NotificationManager.shared.showError(
                title: "Hammerspoon 未運行",
                message: "請確保 Hammerspoon 已安裝並正在運行"
            )
            return
        }

        // 使用 URL Scheme 執行 Lua 代碼
        let encodedCode = code.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = "hammerspoon://runLua?code=\(encodedCode)"

        if let url = URL(string: urlString) {
            NSWorkspace.shared.open(url)
        }
    }

    func saveCurrentLayout(profileName: String) {
        let luaCode = "saveWindowLayout('\(profileName)')"
        executeLuaCode(luaCode)

        // 延遲顯示成功通知
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            NotificationManager.shared.showSuccess(
                title: "佈局已儲存",
                message: "Profile「\(profileName)」已成功儲存"
            )
        }
    }

    func restoreLayout(profileName: String) {
        let luaCode = "restoreWindowLayout('\(profileName)')"
        executeLuaCode(luaCode)

        // 延遲顯示成功通知
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
            NotificationManager.shared.showSuccess(
                title: "佈局已還原",
                message: "Profile「\(profileName)」已成功還原"
            )
            NotificationManager.shared.playCompletionSound()
        }
    }

    func reloadConfiguration() {
        let luaCode = "hs.reload()"
        executeLuaCode(luaCode)
    }

    // MARK: - Settings Sync

    func updateAlertSettings(textSize: Double, duration: Double) {
        let luaCode = """
        alertStyle = { textSize = \(Int(textSize)), duration = \(duration) }
        """
        executeLuaCode(luaCode)
    }

    // MARK: - Keyboard Shortcuts

    func getKeyboardShortcuts() -> [KeyboardShortcut] {
        // 從 init.lua 解析快捷鍵配置
        return [
            KeyboardShortcut(profile: "General", saveKeys: "⌘⌥1", restoreKeys: "⌘⌥⇧1"),
            KeyboardShortcut(profile: "Coding", saveKeys: "⌘⌥2", restoreKeys: "⌘⌥⇧2"),
            KeyboardShortcut(profile: "Comms", saveKeys: "⌘⌥3", restoreKeys: "⌘⌥⇧3")
        ]
    }
}

// MARK: - Supporting Types

struct KeyboardShortcut {
    let profile: String
    let saveKeys: String
    let restoreKeys: String
}