import AppKit
import SwiftUI

class AppIconProvider {
    static func getIcon(for bundleID: String) -> NSImage? {
        guard let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: bundleID) else {
            return nil
        }
        
        return NSWorkspace.shared.icon(forFile: appURL.path)
    }
    
    static func getIcon(for appName: String) -> NSImage? {
        let runningApps = NSWorkspace.shared.runningApplications
        
        if let app = runningApps.first(where: { $0.localizedName == appName }) {
            return app.icon
        }
        
        return NSImage(systemSymbolName: "app", accessibilityDescription: appName)
    }
}