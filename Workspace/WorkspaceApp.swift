func applicationDidFinishLaunching(_ notification: Notification) {
    // 隱藏 Dock 圖示，只在選單列顯示
    NSApp.setActivationPolicy(.accessory)
    
    setupMenuBar()
    
    // 檢查是否首次啟動
    let hasCompletedSetup = UserDefaults.standard.bool(forKey: "hasCompletedSetup")
    if !hasCompletedSetup {
        showWelcomeWindow()
    } else {
        checkHammerspoonInstallation()
    }
}

private func showWelcomeWindow() {
    let welcomeWindow = NSWindow(
        contentRect: NSRect(x: 0, y: 0, width: 400, height: 350),
        styleMask: [.titled, .closable],
        backing: .buffered,
        defer: false
    )
    
    welcomeWindow.title = "Workspace 設定"
    welcomeWindow.contentView = NSHostingView(rootView: WelcomeView())
    welcomeWindow.center()
    welcomeWindow.makeKeyAndOrderFront(nil)
    
    // 確保視窗在最前面
    NSApp.activate(ignoringOtherApps: true)
}