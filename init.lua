-- =============================================================================
--      Hammerspoon Window Layout Manager v5.2 (Manual Profile Hotkeys)
-- =============================================================================
-- 描述:
-- 最終可靠版。由於自動檢測 Space 的 API 在當前環境不穩定，
-- 此版本改為為每個 Profile 設定專屬的儲存/還原快捷鍵。
-- 這是最直接、最可靠的多 Profile 管理方案。
-- =============================================================================

-- --- ★ 使用者設定區 ★ ---
local layouts_path = os.getenv("HOME") .. "/.hammerspoon/layouts/"
hs.fs.mkdir(layouts_path)

-- --- 全域變數 ---
local layoutTimer = nil
local alertStyle = { textSize = 64, duration = 0.5 }


-- --- 核心功能函式 (現在接受 profileName 作為參數) ---

function saveWindowLayout(profileName)
    if not profileName then hs.alert.show("❌ 未提供 Profile 名稱"); return end
    
    local layoutFile = layouts_path .. profileName .. ".json"
    hs.alert.show("⏳ 正在掃描並儲存 Profile '"..profileName.."'...", {textSize = alertStyle.textSize, duration=1})

    if layoutTimer and layoutTimer:running() then layoutTimer:stop() end

    local co = coroutine.create(function()
        -- 我們儲存的是所有可見視窗，不再區分 Space
        local allWindows = hs.window.allWindows()
        local windowsToSave = {}
        
        for _, win in ipairs(allWindows) do
            if not win:isMinimized() then
                local app = win:application()
                if app and app:bundleID() ~= "com.apple.finder" and win:title() ~= "" and win:subrole() == "AXStandardWindow" then
                    local progressText = string.format("💾 儲存中: %s", win:title())
                    hs.alert.show(progressText, alertStyle)
                    coroutine.yield()
                    
                    local frameData = { x = win:frame().x, y = win:frame().y, w = win:frame().w, h = win:frame().h }
                    table.insert(windowsToSave, {
                        app = app:name(), bundleID = app:bundleID(), title = win:title(), frame = frameData
                    })
                end
            end
        end

        local jsonString = hs.json.encode(windowsToSave)
        if jsonString then
            local file, err = io.open(layoutFile, "w")
            if file then file:write(jsonString); file:close() end
            hs.alert.show("✅ Profile '"..profileName.."' 已儲存 (" .. #windowsToSave .. " 個視窗)", {textSize = alertStyle.textSize, duration=2})
        end
    end)
    
    layoutTimer = hs.timer.new(alertStyle.duration, function()
        local status, err = coroutine.resume(co)
        if not status or coroutine.status(co) == "dead" then layoutTimer:stop() end
    end)
    layoutTimer:start()
end

function restoreWindowLayout(profileName)
    if not profileName then hs.alert.show("❌ 未提供 Profile 名稱"); return end

    local layoutFile = layouts_path .. profileName .. ".json"
    hs.alert.show("🚀 準備還原 Profile '"..profileName.."'...", {textSize = alertStyle.textSize, duration=1})
    
    local file, err = io.open(layoutFile, "r"); if not file then hs.alert.show("❌ 找不到 Profile '"..profileName.."'"); return end
    local content = file:read("*a"); file:close()
    local layouts = hs.json.decode(content); if not layouts then return end
    
    local appsToLaunch = {}
    for _, savedLayout in ipairs(layouts) do
        if not hs.application.get(savedLayout.bundleID) then appsToLaunch[savedLayout.bundleID] = true end
    end
    
    if next(appsToLaunch) ~= nil then
        hs.alert.show("⏳ 正在啟動應用...", {textSize = alertStyle.textSize, duration=5})
        for bundleID, _ in pairs(appsToLaunch) do hs.application.launch(bundleID) end
        hs.timer.doAfter(5.0, function() applyLayouts(layouts, profileName) end)
    else
        applyLayouts(layouts, profileName)
    end
end

function applyLayouts(layouts, profileName)
    -- ... (此函式無需改動) ...
    if layoutTimer and layoutTimer:running() then layoutTimer:stop() end
    local co = coroutine.create(function()
        local currentWindowsMap = {}; for _, app in ipairs(hs.application.runningApplications()) do for _, win in ipairs(app:allWindows()) do local key = app:name() .. "|||" .. win:title(); currentWindowsMap[key] = win end end
        for i, savedLayout in ipairs(layouts) do
            local key = savedLayout.app .. "|||" .. savedLayout.title; local targetWindow = currentWindowsMap[key]
            if targetWindow then local progressText = string.format("🔧 還原中 (%d/%d)\n%s", i, #layouts, savedLayout.title); hs.alert.show(progressText, alertStyle); coroutine.yield(); targetWindow:setFrame(savedLayout.frame) end
        end
        hs.alert.show("✅ Profile '"..profileName.."' 還原完畢", {textSize = alertStyle.textSize, duration=2})
    end)
    layoutTimer = hs.timer.new(alertStyle.duration, function() local status, err = coroutine.resume(co); if not status or coroutine.status(co) == "dead" then layoutTimer:stop() end end)
    layoutTimer:start()
end

-- --- ★ 快捷鍵綁定 (核心改動) ★ ---
-- 為你的 Profile 設定快捷鍵。
-- 提示: 使用 Hyper Key (Cmd+Opt+Ctrl+Shift) 可以創造很多不衝突的快捷鍵。

-- Profile 1: "General" (通用)
-- 儲存: Hyper + 1
hs.hotkey.bind({"cmd", "alt"}, "1", function()
    saveWindowLayout("General")
end)
-- 還原: Hyper + Option + 1
hs.hotkey.bind({"cmd", "alt", "shift"}, "1", function()
    restoreWindowLayout("General")
end)

-- Profile 2: "Coding"
-- 儲存: Hyper + C
hs.hotkey.bind({"cmd", "alt"}, "2", function()
    saveWindowLayout("Coding")
end)
-- 還原: Hyper + Option + C
hs.hotkey.bind({"cmd", "alt", "shift"}, "2", function()
    restoreWindowLayout("Coding")
end)

-- Profile 3: "Comms" (通訊)
-- 儲存: Hyper + M
hs.hotkey.bind({"cmd", "alt"}, "3", function()
    saveWindowLayout("Comms")
end)
-- 還原: Hyper + Option + M
hs.hotkey.bind({"cmd", "alt", "shift"}, "1", function()
    restoreWindowLayout("Comms")
end)


-- --- 啟動提示 ---
hs.alert.show("Hammerspoon 工作區管理器已載入！ (v5.2 手動版)", {textSize = 20, duration=3})