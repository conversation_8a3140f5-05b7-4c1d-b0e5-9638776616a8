[{"app": "Code", "frame": {"y": -1414, "w": 1720, "x": -1080, "h": 1415}, "title": "Web Anywhere Door", "bundleID": "com.microsoft.VSCode"}, {"app": "Code", "frame": {"y": -1415, "w": 3440, "x": -1080, "h": 1415}, "title": "Summary • Untitled-1 — memecreater", "bundleID": "com.microsoft.VSCode"}, {"app": "LINE", "frame": {"y": 34, "w": 969, "x": 470, "h": 860}, "title": "LINE", "bundleID": "jp.naver.line.mac"}, {"app": "Google Chrome", "frame": {"y": -1415, "w": 1720, "x": 640, "h": 1415}, "title": "macOS Space 自動化工具 | Google AI Studio - Google Chrome - 柏宏 (<PERSON><PERSON>)", "bundleID": "com.google.Chrome"}, {"app": "Google Chrome", "frame": {"y": -1415, "w": 1720, "x": -1080, "h": 1415}, "title": "收件匣 - <EMAIL> - QT Medical, Inc. 郵件 - High memory usage - 1.8 GB - Google Chrome - 柏宏 (<PERSON><PERSON>)", "bundleID": "com.google.Chrome"}, {"app": "Google Chrome", "frame": {"y": -1415, "w": 1720, "x": -1080, "h": 1415}, "title": "歐美長輩圖：文化現象分析 | Google AI Studio - Google Chrome - 柏宏 (<PERSON><PERSON>)", "bundleID": "com.google.Chrome"}, {"app": "Notion", "frame": {"y": 25, "w": 1440, "x": 0, "h": 875}, "title": "Space Lighthouse", "bundleID": "notion.id"}, {"app": "Warp", "frame": {"y": -1012, "w": 1440, "x": -392, "h": 875}, "title": "..n@Mac:~/Desktop/github", "bundleID": "dev.warp.Warp-Stable"}, {"app": "<PERSON><PERSON><PERSON>", "frame": {"y": -1415, "w": 1720, "x": -1080, "h": 1415}, "title": "init.lua — .hammerspoon", "bundleID": "com.todesktop.230313mzl4w4u92"}, {"app": "<PERSON><PERSON><PERSON>", "frame": {"y": -1415, "w": 1720, "x": -1080, "h": 1415}, "title": "restore_layout.sh — Space-Lighthouse", "bundleID": "com.todesktop.230313mzl4w4u92"}, {"app": "Hammerspoon", "frame": {"y": -1415, "w": 510, "x": 1157, "h": 521}, "title": "Hammerspoon Console", "bundleID": "org.hammerspoon.Hammerspoon"}]